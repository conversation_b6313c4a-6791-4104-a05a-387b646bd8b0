#!/bin/bash

# PosterCraft 启动脚本
# 用于在macOS/Linux系统上启动PosterCraft图形界面启动器

echo "🎨 启动 PosterCraft 启动器..."

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 检查Python环境
if [ ! -f "postercraft_official/bin/python" ]; then
    echo "❌ 错误: 未找到虚拟环境"
    echo "请确保已正确安装PosterCraft环境"
    echo "虚拟环境路径: $SCRIPT_DIR/postercraft_official"
    read -p "按回车键退出..."
    exit 1
fi

# 检查启动器文件
if [ ! -f "postercraft_launcher.py" ]; then
    echo "❌ 错误: 未找到启动器文件"
    echo "请确保 postercraft_launcher.py 文件存在"
    read -p "按回车键退出..."
    exit 1
fi

# 安装必要的依赖（如果需要）
echo "📦 检查依赖..."
./postercraft_official/bin/python -c "import tkinter, psutil" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "📦 安装缺失的依赖..."
    ./postercraft_official/bin/pip install psutil
fi

# 启动图形界面启动器
echo "🚀 启动图形界面..."
./postercraft_official/bin/python postercraft_launcher.py

echo "👋 PosterCraft 启动器已退出"
