# 🎨 PosterCraft 图形界面启动器

这是一个用户友好的图形界面启动器，让您无需手动输入命令即可轻松启动PosterCraft AI海报生成器。

## 📋 功能特性

### 🖥️ 图形界面
- **直观的用户界面** - 清晰的状态显示和控制按钮
- **实时状态监控** - 显示应用运行状态和访问地址
- **日志显示** - 实时查看应用启动和运行日志
- **一键操作** - 启动、停止、重启应用

### 🚀 智能启动
- **环境检测** - 自动检查虚拟环境和必要文件
- **依赖管理** - 自动安装缺失的依赖包
- **错误处理** - 友好的错误提示和解决建议
- **自动打开浏览器** - 启动成功后可选择自动打开Web界面

### 📊 状态监控
- **端口检测** - 自动检测应用是否在运行
- **进程管理** - 安全的启动和停止操作
- **日志过滤** - 过滤无关信息，显示重要日志

## 🚀 使用方法

### 方法一：双击启动脚本（推荐）

#### macOS/Linux:
```bash
# 双击运行
./start_postercraft.sh
```

#### Windows:
```batch
# 双击运行
start_postercraft.bat
```

### 方法二：直接运行Python启动器

```bash
# 激活虚拟环境并运行启动器
source postercraft_official/bin/activate  # macOS/Linux
# 或
postercraft_official\Scripts\activate.bat  # Windows

python postercraft_launcher.py
```

## 🎯 使用流程

1. **启动启动器**
   - 双击 `start_postercraft.sh` (macOS/Linux) 或 `start_postercraft.bat` (Windows)
   - 或直接运行 `python postercraft_launcher.py`

2. **启动PosterCraft**
   - 点击 "🚀 启动 PosterCraft" 按钮
   - 等待应用加载完成（约1-2分钟）
   - 看到 "✅ PosterCraft 启动成功!" 消息

3. **访问Web界面**
   - 点击 "🌐 打开浏览器" 按钮
   - 或手动访问 http://localhost:7860

4. **开始创作**
   - 在Web界面中输入提示词
   - 调整参数设置
   - 点击生成按钮开始创作

## 📱 界面说明

### 状态区域
- **运行状态**: 显示应用当前状态（运行中/未启动）
- **访问地址**: 显示Web界面访问地址

### 控制按钮
- **🚀 启动 PosterCraft**: 启动/停止应用
- **🌐 打开浏览器**: 在浏览器中打开Web界面
- **🔄 重启应用**: 重新启动应用

### 应用信息
- 显示PosterCraft的功能特性和使用建议

### 运行日志
- 实时显示应用启动和运行日志
- 可以清除日志内容

## ⚠️ 注意事项

1. **首次启动**
   - 首次启动可能需要较长时间加载模型
   - 请耐心等待，不要重复点击启动按钮

2. **系统要求**
   - 确保已正确安装PosterCraft环境
   - 需要Python 3.8+和相关依赖

3. **端口占用**
   - 应用使用7860端口，确保端口未被其他程序占用
   - 如果端口被占用，请先停止占用端口的程序

4. **内存使用**
   - 应用会占用较多内存（特别是启用Qwen模型时）
   - 建议关闭其他不必要的程序

## 🔧 故障排除

### 启动失败
- 检查虚拟环境是否正确安装
- 确认所有模型文件已下载完成
- 查看日志中的错误信息

### 无法访问Web界面
- 确认应用已成功启动
- 检查防火墙设置
- 尝试重启应用

### 模型加载失败
- 检查模型文件完整性
- 确认模型路径配置正确
- 查看详细错误日志

## 📞 技术支持

如果遇到问题，请：
1. 查看运行日志中的错误信息
2. 检查系统环境和依赖
3. 参考PosterCraft主要文档

---

**享受AI创作的乐趣！** 🎨✨
