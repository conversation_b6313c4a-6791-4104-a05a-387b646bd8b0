#!/bin/bash

# 创建PosterCraft桌面快捷方式脚本

echo "🎨 创建PosterCraft桌面快捷方式"
echo "================================"

# 获取当前目录
CURRENT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DESKTOP_DIR="$HOME/Desktop"

# 创建桌面快捷方式
SHORTCUT_PATH="$DESKTOP_DIR/启动PosterCraft.command"

# 创建快捷方式内容
cat > "$SHORTCUT_PATH" << 'EOF'
#!/bin/bash

# PosterCraft 桌面快捷方式
# 自动定位到PosterCraft目录并启动

# 查找PosterCraft目录
POSTERCRAFT_DIR=""

# 常见的可能位置
POSSIBLE_DIRS=(
    "$HOME/PosterCraft"
    "$HOME/Desktop/PosterCraft"
    "$HOME/Documents/PosterCraft"
    "$HOME/Downloads/PosterCraft"
)

# 搜索PosterCraft目录
for dir in "${POSSIBLE_DIRS[@]}"; do
    if [ -d "$dir" ] && [ -f "$dir/postercraft_launcher.py" ]; then
        POSTERCRAFT_DIR="$dir"
        break
    fi
done

# 如果没找到，让用户选择
if [ -z "$POSTERCRAFT_DIR" ]; then
    echo "未找到PosterCraft目录，请手动选择..."
    echo "请将此快捷方式移动到PosterCraft项目目录中，然后重新运行。"
    echo ""
    echo "按任意键退出..."
    read -n 1
    exit 1
fi

echo "找到PosterCraft目录: $POSTERCRAFT_DIR"
cd "$POSTERCRAFT_DIR"

# 执行启动程序
if [ -f "启动PosterCraft.command" ]; then
    ./启动PosterCraft.command
else
    echo "未找到启动程序，直接运行启动器..."
    if [ -f "postercraft_official/bin/python" ] && [ -f "postercraft_launcher.py" ]; then
        ./postercraft_official/bin/python postercraft_launcher.py
    else
        echo "❌ 环境不完整，请检查PosterCraft安装"
        echo "按任意键退出..."
        read -n 1
        exit 1
    fi
fi
EOF

# 设置执行权限
chmod +x "$SHORTCUT_PATH"

echo ""
echo "✅ 桌面快捷方式创建成功！"
echo "📍 位置: $SHORTCUT_PATH"
echo ""
echo "现在你可以："
echo "1. 在桌面上双击 '启动PosterCraft.command' 来启动应用"
echo "2. 或者直接双击项目目录中的 '启动PosterCraft.command'"
echo "3. 或者双击 'PosterCraft启动器.app' 应用程序包"
echo ""
echo "按任意键退出..."
read -n 1
