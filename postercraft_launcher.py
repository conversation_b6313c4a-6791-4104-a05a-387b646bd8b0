#!/usr/bin/env python3
"""
PosterCraft 启动器
一个图形界面启动器，让用户轻松启动PosterCraft应用
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import subprocess
import threading
import os
import sys
import time
import webbrowser
from pathlib import Path
import signal
import psutil

class PosterCraftLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("PosterCraft 启动器")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 设置应用图标和样式
        self.setup_style()
        
        # 应用状态
        self.process = None
        self.is_running = False
        self.app_url = "http://localhost:7860"
        
        # 项目路径
        self.project_path = Path(__file__).parent
        self.venv_path = self.project_path / "postercraft_official"
        self.python_path = self.venv_path / "bin" / "python"
        self.demo_script = self.project_path / "demo_gradio.py"
        
        # 创建界面
        self.create_widgets()
        
        # 启动状态检查
        self.check_status()
        
    def setup_style(self):
        """设置界面样式"""
        self.root.configure(bg='#f0f0f0')
        
        # 配置ttk样式
        style = ttk.Style()
        style.theme_use('clam')
        
        # 自定义样式
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'), background='#f0f0f0')
        style.configure('Status.TLabel', font=('Arial', 10), background='#f0f0f0')
        style.configure('Success.TLabel', font=('Arial', 10), background='#f0f0f0', foreground='green')
        style.configure('Error.TLabel', font=('Arial', 10), background='#f0f0f0', foreground='red')
        style.configure('Start.TButton', font=('Arial', 12, 'bold'))
        
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🎨 PosterCraft AI海报生成器", style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 状态信息框架
        status_frame = ttk.LabelFrame(main_frame, text="应用状态", padding="10")
        status_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        status_frame.columnconfigure(1, weight=1)
        
        # 状态标签
        ttk.Label(status_frame, text="运行状态:", style='Status.TLabel').grid(row=0, column=0, sticky=tk.W)
        self.status_label = ttk.Label(status_frame, text="未启动", style='Error.TLabel')
        self.status_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        ttk.Label(status_frame, text="访问地址:", style='Status.TLabel').grid(row=1, column=0, sticky=tk.W)
        self.url_label = ttk.Label(status_frame, text=self.app_url, style='Status.TLabel')
        self.url_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        
        # 控制按钮框架
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=2, column=0, columnspan=3, pady=10)
        
        # 启动/停止按钮
        self.start_button = ttk.Button(control_frame, text="🚀 启动 PosterCraft", 
                                      command=self.toggle_app, style='Start.TButton')
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 打开浏览器按钮
        self.browser_button = ttk.Button(control_frame, text="🌐 打开浏览器", 
                                        command=self.open_browser, state='disabled')
        self.browser_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 重启按钮
        self.restart_button = ttk.Button(control_frame, text="🔄 重启应用", 
                                        command=self.restart_app, state='disabled')
        self.restart_button.pack(side=tk.LEFT)
        
        # 信息框架
        info_frame = ttk.LabelFrame(main_frame, text="应用信息", padding="10")
        info_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        info_frame.columnconfigure(0, weight=1)
        
        info_text = """
🎯 功能特性:
• AI海报生成 - 使用FLUX.1模型生成高质量海报
• 智能提示词重写 - Qwen2.5-3B-Instruct模型优化您的创意描述
• 中英文支持 - 完全支持中文和英文提示词
• 参数调节 - 自定义尺寸、推理步数、引导比例等

⚡ 使用建议:
• 推荐设置: 832x1216尺寸, 15-18推理步数
• CPU环境下生成时间约2-2.5小时
• 启用Prompt Recap可获得更好的生成效果
        """
        
        info_label = ttk.Label(info_frame, text=info_text.strip(), style='Status.TLabel', justify=tk.LEFT)
        info_label.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        # 日志框架
        log_frame = ttk.LabelFrame(main_frame, text="运行日志", padding="10")
        log_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=10, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 清除日志按钮
        clear_button = ttk.Button(log_frame, text="清除日志", command=self.clear_log)
        clear_button.grid(row=1, column=0, pady=(5, 0))
        
    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def clear_log(self):
        """清除日志"""
        self.log_text.delete(1.0, tk.END)
        
    def check_status(self):
        """检查应用运行状态"""
        try:
            # 检查端口7860是否被占用
            for conn in psutil.net_connections():
                if conn.laddr.port == 7860 and conn.status == 'LISTEN':
                    self.is_running = True
                    self.update_status(True)
                    return
        except:
            pass
        
        self.is_running = False
        self.update_status(False)
        
        # 定期检查状态
        self.root.after(5000, self.check_status)
        
    def update_status(self, running):
        """更新状态显示"""
        if running:
            self.status_label.config(text="运行中", style='Success.TLabel')
            self.start_button.config(text="🛑 停止 PosterCraft")
            self.browser_button.config(state='normal')
            self.restart_button.config(state='normal')
        else:
            self.status_label.config(text="未启动", style='Error.TLabel')
            self.start_button.config(text="🚀 启动 PosterCraft")
            self.browser_button.config(state='disabled')
            self.restart_button.config(state='disabled')
            
    def toggle_app(self):
        """切换应用状态（启动/停止）"""
        if self.is_running:
            self.stop_app()
        else:
            self.start_app()
            
    def start_app(self):
        """启动应用"""
        if not self.validate_environment():
            return
            
        self.log_message("正在启动 PosterCraft...")
        self.start_button.config(state='disabled')
        
        # 在新线程中启动应用
        threading.Thread(target=self._start_app_thread, daemon=True).start()

    def _start_app_thread(self):
        """启动应用的线程函数"""
        try:
            # 构建启动命令
            if os.name == 'nt':  # Windows
                cmd = f'"{self.python_path}" "{self.demo_script}"'
                shell = True
            else:  # macOS/Linux
                cmd = [str(self.python_path), str(self.demo_script)]
                shell = False

            # 设置环境变量
            env = os.environ.copy()
            env['HF_HOME'] = str(Path.home() / "AI_Models" / "PosterCraft")
            env['HUGGINGFACE_HUB_CACHE'] = str(Path.home() / "AI_Models" / "PosterCraft")
            env['TRANSFORMERS_CACHE'] = str(Path.home() / "AI_Models" / "PosterCraft")

            self.log_message("激活虚拟环境...")
            self.log_message("设置模型缓存路径...")
            self.log_message("启动 demo_gradio.py...")

            # 启动进程
            self.process = subprocess.Popen(
                cmd,
                shell=shell,
                env=env,
                cwd=str(self.project_path),
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )

            # 监控输出
            self._monitor_output()

        except Exception as e:
            self.log_message(f"启动失败: {str(e)}")
            self.root.after(0, lambda: self.start_button.config(state='normal'))

    def _monitor_output(self):
        """监控应用输出"""
        try:
            for line in iter(self.process.stdout.readline, ''):
                if line:
                    # 过滤和格式化输出
                    clean_line = line.strip()
                    if clean_line and not self._should_filter_line(clean_line):
                        self.root.after(0, lambda msg=clean_line: self.log_message(msg))

                    # 检查是否启动成功
                    if "Running on local URL" in line:
                        self.root.after(0, lambda: self._on_app_started())

                if self.process.poll() is not None:
                    break

        except Exception as e:
            self.root.after(0, lambda: self.log_message(f"监控输出时出错: {str(e)}"))
        finally:
            self.root.after(0, lambda: self._on_app_stopped())

    def _should_filter_line(self, line):
        """判断是否应该过滤某行输出"""
        filter_keywords = [
            "FutureWarning",
            "UserWarning",
            "Loading checkpoint shards",
            "Loading pipeline components",
            "HTTP Request:",
            "You set `add_prefix_space`"
        ]
        return any(keyword in line for keyword in filter_keywords)

    def _on_app_started(self):
        """应用启动成功回调"""
        self.is_running = True
        self.update_status(True)
        self.start_button.config(state='normal')
        self.log_message("✅ PosterCraft 启动成功!")
        self.log_message(f"🌐 访问地址: {self.app_url}")

        # 询问是否自动打开浏览器
        if messagebox.askyesno("启动成功", "PosterCraft 已成功启动!\n是否立即打开浏览器?"):
            self.open_browser()

    def _on_app_stopped(self):
        """应用停止回调"""
        self.is_running = False
        self.process = None
        self.update_status(False)
        self.start_button.config(state='normal')
        self.log_message("❌ PosterCraft 已停止")

    def stop_app(self):
        """停止应用"""
        if self.process:
            self.log_message("正在停止 PosterCraft...")
            self.start_button.config(state='disabled')

            try:
                # 优雅地终止进程
                if os.name == 'nt':  # Windows
                    self.process.terminate()
                else:  # macOS/Linux
                    self.process.send_signal(signal.SIGTERM)

                # 等待进程结束
                try:
                    self.process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    self.log_message("强制终止进程...")
                    self.process.kill()
                    self.process.wait()

            except Exception as e:
                self.log_message(f"停止应用时出错: {str(e)}")
            finally:
                self._on_app_stopped()

    def restart_app(self):
        """重启应用"""
        self.log_message("正在重启 PosterCraft...")
        if self.is_running:
            self.stop_app()
            # 等待停止完成后再启动
            self.root.after(2000, self.start_app)
        else:
            self.start_app()

    def open_browser(self):
        """打开浏览器"""
        try:
            webbrowser.open(self.app_url)
            self.log_message(f"已打开浏览器: {self.app_url}")
        except Exception as e:
            self.log_message(f"打开浏览器失败: {str(e)}")
            messagebox.showerror("错误", f"无法打开浏览器: {str(e)}")

    def validate_environment(self):
        """验证运行环境"""
        # 检查Python可执行文件
        if not self.python_path.exists():
            self.log_message("❌ 未找到虚拟环境Python")
            messagebox.showerror("环境错误",
                               f"未找到虚拟环境Python:\n{self.python_path}\n\n"
                               "请确保已正确安装PosterCraft环境")
            return False

        # 检查demo脚本
        if not self.demo_script.exists():
            self.log_message("❌ 未找到demo_gradio.py")
            messagebox.showerror("文件错误",
                               f"未找到启动脚本:\n{self.demo_script}")
            return False

        # 检查模型目录
        model_dir = Path.home() / "AI_Models" / "PosterCraft"
        if not model_dir.exists():
            self.log_message("⚠️  模型目录不存在，将在首次运行时创建")

        return True

    def on_closing(self):
        """窗口关闭事件"""
        if self.is_running:
            if messagebox.askyesno("确认退出", "PosterCraft 正在运行中。\n是否停止应用并退出?"):
                self.stop_app()
                self.root.destroy()
        else:
            self.root.destroy()

    def run(self):
        """运行启动器"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.log_message("🎨 PosterCraft 启动器已就绪")
        self.log_message("点击'启动 PosterCraft'开始使用")
        self.root.mainloop()

def main():
    """主函数"""
    try:
        launcher = PosterCraftLauncher()
        launcher.run()
    except Exception as e:
        messagebox.showerror("启动器错误", f"启动器初始化失败:\n{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
