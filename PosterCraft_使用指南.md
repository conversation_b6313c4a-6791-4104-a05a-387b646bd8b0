# PosterCraft 详细使用指南

## 🎯 快速开始

### 启动应用
1. 打开终端，进入项目目录：`cd /Users/<USER>/PosterCraft`
2. 激活虚拟环境：`source postercraft_official/bin/activate`
3. 启动应用：`python demo_gradio.py`
4. 打开浏览器访问：http://localhost:7860

## 🎨 界面详解

### 左侧配置面板

#### 1. Prompt (提示词)
**作用**: 描述您想要生成的海报内容
**支持语言**: 中文、英文
**建议长度**: 10-100字

**示例提示词**:
```
中文示例:
- "科技感未来城市海报，蓝色调，简约设计"
- "咖啡店宣传海报，温馨氛围，复古风格"
- "音乐节海报，摇滚风格，黑色背景，霓虹灯效果"

英文示例:
- "Modern tech conference poster with blue gradient background"
- "Vintage coffee shop advertisement with warm colors"
- "Rock music festival poster with neon lights and dark theme"
```

**提示词技巧**:
- ✅ 包含主题、风格、颜色、氛围
- ✅ 具体描述比抽象词汇效果更好
- ✅ 可以指定文字内容，如"标题写着'音乐节'"
- ❌ 避免过于复杂或矛盾的描述

#### 2. Enable Prompt Recap (提示词重写)
**状态**: 🔴 当前禁用
**建议**: 保持未勾选状态
**原因**: Qwen模型暂时有问题，直接使用原始提示词效果更好

#### 3. Width & Height (图像尺寸)
**默认**: 832 x 1216 (海报比例)
**单位**: 像素
**调整步长**: 64像素

**常用尺寸推荐**:
```
海报尺寸:
- 832 x 1216 (标准海报) - 推荐
- 768 x 1024 (小海报)
- 1024 x 1536 (大海报)

方形尺寸:
- 512 x 512 (小方图) - 最快
- 768 x 768 (标准方图)
- 1024 x 1024 (大方图)

横向尺寸:
- 1216 x 832 (横版海报)
- 1024 x 768 (横版标准)
```

**性能影响**:
- 尺寸越大 → 生成时间越长 → 质量越高
- 尺寸越小 → 生成时间越短 → 适合测试

#### 4. Inference Steps (推理步数) ⭐ 重要
**作用**: 控制图像生成的精细程度
**范围**: 1-100步
**默认**: 28步

**详细设置指南**:
```
快速测试模式: 8-12步
├─ 生成时间: 30分钟-1小时
├─ 图像质量: 基础质量，适合测试提示词
└─ 适用场景: 快速验证想法、测试不同提示词

日常使用模式: 15-20步 ⭐ 推荐
├─ 生成时间: 1.5-2.5小时
├─ 图像质量: 良好质量，细节丰富
└─ 适用场景: 日常创作、分享使用

高质量模式: 25-30步
├─ 生成时间: 3-4小时
├─ 图像质量: 高质量，细节精细
└─ 适用场景: 重要作品、专业用途

专业级模式: 35+步
├─ 生成时间: 4+小时
├─ 图像质量: 最高质量
└─ 适用场景: 商业作品、展示用途
```

**选择建议**:
- 🔰 新手: 从12步开始测试
- 📱 日常: 使用18步最佳平衡
- 🎨 专业: 25步以上追求质量

#### 5. Guidance Scale (引导比例)
**作用**: 控制AI对提示词的遵循程度
**范围**: 0.0-20.0
**默认**: 3.5
**推荐范围**: 2.0-7.0

**设置效果**:
```
低值 (1.0-3.0):
├─ 效果: 更有创意，但可能偏离提示词
├─ 适用: 希望AI自由发挥
└─ 风险: 结果可能不符合预期

中值 (3.0-5.0): ⭐ 推荐
├─ 效果: 平衡创意和准确性
├─ 适用: 大多数情况
└─ 推荐: 3.5-4.5最佳

高值 (5.0-10.0):
├─ 效果: 严格遵循提示词
├─ 适用: 需要精确控制结果
└─ 风险: 可能过于死板

极高值 (10.0+):
├─ 效果: 可能产生过度饱和或失真
└─ 不推荐: 除非特殊需求
```

#### 6. Seed (随机种子)
**作用**: 控制随机性，相同种子产生相同结果
**默认**: 空白 (随机)
**输入**: 整数或-1

**使用场景**:
```
留空或-1 (推荐):
├─ 每次生成不同结果
└─ 适合: 探索不同可能性

固定数字 (如: 42, 123):
├─ 每次生成相同结果
├─ 适用: 微调其他参数时保持图像一致
└─ 用途: 对比不同设置的效果
```

### 右侧结果面板

#### Generated Image (生成图像)
- 显示最终生成的海报
- 支持下载保存
- 可以右键保存到本地

#### Final Prompt Used (最终使用的提示词)
- 显示实际用于生成的提示词
- 如果启用Recap会显示重写后的提示词
- 当前显示原始输入的提示词

#### Status Log (状态日志)
- 显示生成过程信息
- 包含错误信息和进度提示
- 帮助诊断问题

## 🎯 实用生成策略

### 新手入门流程
1. **第一次测试**: 512x512, 10步, 简单提示词
2. **效果满意**: 增加到832x1216, 18步
3. **追求质量**: 使用25步生成最终作品

### 高效工作流程
1. **快速测试** (10步): 验证提示词效果
2. **中等质量** (18步): 确认构图和风格
3. **最终输出** (25步): 生成高质量成品

### 参数组合推荐
```
快速预览组合:
- 尺寸: 512x512
- 步数: 10
- 引导: 3.5
- 时间: ~30分钟

日常创作组合: ⭐
- 尺寸: 832x1216  
- 步数: 18
- 引导: 4.0
- 时间: ~2小时

专业作品组合:
- 尺寸: 832x1216
- 步数: 28
- 引导: 3.5
- 时间: ~3.5小时
```

## ⚠️ 注意事项

### 性能相关
- CPU模式运行较慢，请耐心等待
- 生成过程中不要关闭浏览器
- 可以在终端查看详细进度

### 常见问题
1. **生成时间过长**: 降低步数和尺寸
2. **结果不满意**: 调整提示词描述
3. **应用无响应**: 刷新页面或重启应用

### 最佳实践
- 先用小尺寸测试提示词效果
- 保存满意的参数组合
- 记录好用的提示词模板

## 🎨 提示词创作技巧

### 提示词结构模板
```
基础结构: [主题] + [风格] + [颜色] + [氛围]
示例: "音乐节海报 + 现代简约风格 + 蓝紫色调 + 动感活力"

完整结构: [主题] + [风格] + [颜色] + [构图] + [文字] + [细节]
示例: "科技大会海报，未来主义风格，蓝色渐变背景，居中构图，标题'AI Summit 2024'，几何图形装饰"
```

### 不同类型海报的提示词示例

#### 商业海报
```
餐厅: "高端日料餐厅海报，简约日式风格，暖色调，木质纹理背景，优雅字体"
咖啡店: "精品咖啡店宣传海报，工业风格，棕色调，咖啡豆和蒸汽元素"
健身房: "健身房招募海报，动感现代风格，橙色和黑色，肌肉线条，力量感"
```

#### 活动海报
```
音乐节: "摇滚音乐节海报，朋克风格，黑红配色，闪电和吉他元素，狂野氛围"
艺术展: "当代艺术展海报，极简主义，黑白灰色调，几何构图，高级感"
科技会议: "AI科技峰会海报，未来科技风，蓝色光效，电路板纹理，专业感"
```

#### 文化海报
```
电影: "科幻电影海报，赛博朋克风格，霓虹色彩，城市夜景，神秘氛围"
书籍: "文学作品宣传海报，复古风格，暖黄色调，纸质纹理，文艺气息"
展览: "历史文物展海报，古典风格，金棕色调，古典装饰，庄重感"
```

### 颜色搭配指南
```
专业商务: 蓝色 + 白色 + 灰色
时尚潮流: 黑色 + 金色 + 白色
温馨舒适: 米色 + 棕色 + 橙色
活力青春: 橙色 + 黄色 + 红色
自然清新: 绿色 + 白色 + 棕色
科技未来: 蓝色 + 紫色 + 黑色
```

## 🔧 故障排除

### 常见错误及解决方案

#### 1. 生成失败或中断
**现象**: 进度条停止，出现错误信息
**解决方案**:
- 刷新浏览器页面
- 检查提示词是否包含特殊字符
- 降低图像尺寸和步数重试

#### 2. 生成速度极慢
**现象**: 每步耗时超过10分钟
**解决方案**:
- 降低推理步数到10-15步
- 减小图像尺寸到512x768
- 关闭其他占用CPU的程序

#### 3. 结果与预期差距很大
**现象**: 生成的图像完全不符合描述
**解决方案**:
- 调整Guidance Scale到4.0-6.0
- 使用更具体详细的提示词
- 尝试不同的随机种子

#### 4. 应用无法访问
**现象**: 浏览器显示连接失败
**解决方案**:
- 检查终端是否有错误信息
- 重启应用: Ctrl+C 后重新运行
- 确认端口7860未被占用

### 性能优化建议

#### 系统优化
- 关闭不必要的应用程序
- 确保有足够的内存空间
- 避免同时运行其他AI应用

#### 参数优化
```
测试阶段: 512x512, 8步 → 快速验证
调整阶段: 768x768, 15步 → 平衡质量
最终输出: 832x1216, 25步 → 高质量成品
```

## 📚 进阶技巧

### 批量生成策略
1. **固定种子**: 使用相同种子测试不同提示词
2. **渐进优化**: 从低步数开始，逐步提高质量
3. **参数记录**: 记录成功的参数组合

### 创意提升方法
1. **风格混合**: "日式简约 + 现代科技风格"
2. **情感描述**: "温暖的"、"神秘的"、"充满活力的"
3. **具体细节**: 指定字体、布局、装饰元素

### 质量控制
1. **多次生成**: 同样参数生成多张，选择最佳
2. **微调参数**: 小幅调整Guidance Scale
3. **种子探索**: 尝试不同种子值

---
**版本**: v1.0 | **更新日期**: 2025-07-08
**适用环境**: CPU模式 | **推荐配置**: 18步 + 832x1216
