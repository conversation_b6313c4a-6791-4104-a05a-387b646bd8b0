#!/bin/bash

# PosterCraft 快速状态检查脚本

echo "🎨 PosterCraft 状态检查"
echo "======================="

# 查找PosterCraft进程
POSTERCRAFT_PID=$(pgrep -f "demo_gradio.py")

if [ -z "$POSTERCRAFT_PID" ]; then
    echo "❌ PosterCraft进程未运行"
    exit 1
fi

echo "✅ PosterCraft进程ID: $POSTERCRAFT_PID"

# 检查端口
PORT_CHECK=$(lsof -i :7860 2>/dev/null)
if [ -n "$PORT_CHECK" ]; then
    echo "✅ 端口7860正在监听"
else
    echo "❌ 端口7860未监听"
fi

# 检查CPU使用率
echo ""
echo "📊 进程资源使用:"
ps -p $POSTERCRAFT_PID -o pid,pcpu,pmem,etime,command

# 检查系统负载
echo ""
echo "💻 系统状态:"
uptime

# 检查内存使用
echo ""
echo "🧠 内存使用:"
free -h 2>/dev/null || vm_stat | head -5

# 检查Web界面响应
echo ""
echo "🌐 Web界面检查:"
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:7860/ --connect-timeout 5)
if [ "$HTTP_STATUS" = "200" ]; then
    echo "✅ Web界面响应正常"
else
    echo "❌ Web界面无响应 (HTTP: $HTTP_STATUS)"
fi

echo ""
echo "🔍 建议:"
CPU_PERCENT=$(ps -p $POSTERCRAFT_PID -o pcpu= | tr -d ' ')
if (( $(echo "$CPU_PERCENT < 10" | bc -l) )); then
    echo "⚠️  CPU使用率较低 ($CPU_PERCENT%)，可能卡住了"
    echo "   建议重启应用或检查日志"
else
    echo "✅ CPU使用率正常 ($CPU_PERCENT%)，生成进行中"
fi
