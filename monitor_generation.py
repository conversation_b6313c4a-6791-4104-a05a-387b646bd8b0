#!/usr/bin/env python3
"""
PosterCraft 生成监控脚本
监控图像生成进度，检测卡住的情况
"""

import time
import psutil
import requests
import subprocess
import sys
from datetime import datetime, timedelta

class PosterCraftMonitor:
    def __init__(self):
        self.port = 7860
        self.url = f"http://localhost:{self.port}"
        self.process_name = "Python"
        self.script_name = "demo_gradio.py"
        
    def find_postercraft_process(self):
        """查找PosterCraft进程"""
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'cpu_percent', 'memory_percent']):
            try:
                if (proc.info['name'] == self.process_name and 
                    any(self.script_name in cmd for cmd in proc.info['cmdline'])):
                    return proc
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return None
    
    def check_app_responsive(self):
        """检查应用是否响应"""
        try:
            response = requests.get(f"{self.url}/", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def get_system_resources(self):
        """获取系统资源使用情况"""
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        return {
            'cpu_percent': cpu_percent,
            'memory_percent': memory.percent,
            'memory_available_gb': memory.available / (1024**3)
        }
    
    def monitor_generation(self, duration_minutes=180):  # 默认监控3小时
        """监控生成过程"""
        print("🎨 PosterCraft 生成监控器启动")
        print("=" * 50)
        
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=duration_minutes)
        
        last_cpu_check = 0
        low_cpu_count = 0
        
        while datetime.now() < end_time:
            current_time = datetime.now()
            elapsed = current_time - start_time
            
            # 查找进程
            proc = self.find_postercraft_process()
            if not proc:
                print(f"❌ [{current_time.strftime('%H:%M:%S')}] PosterCraft进程未找到")
                break
            
            # 获取进程信息
            try:
                proc_info = proc.as_dict(['pid', 'cpu_percent', 'memory_percent', 'create_time'])
                proc_cpu = proc_info['cpu_percent']
                proc_memory = proc_info['memory_percent']
                
                # 获取系统资源
                sys_resources = self.get_system_resources()
                
                # 检查应用响应
                responsive = self.check_app_responsive()
                
                # 显示状态
                status = "🟢 正常" if responsive else "🔴 无响应"
                print(f"[{current_time.strftime('%H:%M:%S')}] "
                      f"运行时间: {str(elapsed).split('.')[0]} | "
                      f"进程CPU: {proc_cpu:.1f}% | "
                      f"系统CPU: {sys_resources['cpu_percent']:.1f}% | "
                      f"内存: {proc_memory:.1f}% | "
                      f"状态: {status}")
                
                # 检测异常情况
                if proc_cpu < 5.0:  # CPU使用率低于5%
                    low_cpu_count += 1
                    if low_cpu_count >= 5:  # 连续5次检查CPU都很低
                        print(f"⚠️  警告: 进程CPU使用率持续过低 ({proc_cpu:.1f}%)，可能卡住了")
                        
                        # 询问是否重启
                        try:
                            user_input = input("是否重启应用? (y/n): ").lower().strip()
                            if user_input == 'y':
                                print("🔄 重启应用...")
                                proc.terminate()
                                time.sleep(3)
                                if proc.is_running():
                                    proc.kill()
                                self.restart_app()
                                break
                        except KeyboardInterrupt:
                            break
                else:
                    low_cpu_count = 0
                
                # 检查内存使用
                if sys_resources['memory_available_gb'] < 2.0:
                    print(f"⚠️  警告: 系统可用内存不足 ({sys_resources['memory_available_gb']:.1f}GB)")
                
                # 检查运行时间
                if elapsed.total_seconds() > 10800:  # 3小时
                    print("⚠️  警告: 生成时间超过3小时，建议检查")
                
            except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                print(f"❌ 无法获取进程信息: {e}")
                break
            
            time.sleep(30)  # 每30秒检查一次
        
        print("\n🏁 监控结束")
    
    def restart_app(self):
        """重启应用"""
        try:
            subprocess.run([
                "bash", "-c", 
                "cd /Users/<USER>/PosterCraft && source postercraft_official/bin/activate && python demo_gradio.py"
            ], check=False)
        except Exception as e:
            print(f"重启失败: {e}")

def main():
    monitor = PosterCraftMonitor()
    
    print("PosterCraft 生成监控器")
    print("功能:")
    print("- 监控CPU和内存使用")
    print("- 检测应用是否卡住")
    print("- 自动重启功能")
    print("- 资源使用警告")
    print()
    
    try:
        duration = int(input("监控时长(分钟，默认180): ") or "180")
    except ValueError:
        duration = 180
    
    try:
        monitor.monitor_generation(duration)
    except KeyboardInterrupt:
        print("\n👋 监控已停止")

if __name__ == "__main__":
    main()
