@echo off
REM PosterCraft 启动脚本 (Windows)
REM 用于在Windows系统上启动PosterCraft图形界面启动器

title PosterCraft 启动器
echo 🎨 启动 PosterCraft 启动器...

REM 获取脚本所在目录
cd /d "%~dp0"

REM 检查Python环境
if not exist "postercraft_official\Scripts\python.exe" (
    echo ❌ 错误: 未找到虚拟环境
    echo 请确保已正确安装PosterCraft环境
    echo 虚拟环境路径: %CD%\postercraft_official
    pause
    exit /b 1
)

REM 检查启动器文件
if not exist "postercraft_launcher.py" (
    echo ❌ 错误: 未找到启动器文件
    echo 请确保 postercraft_launcher.py 文件存在
    pause
    exit /b 1
)

REM 安装必要的依赖（如果需要）
echo 📦 检查依赖...
postercraft_official\Scripts\python.exe -c "import tkinter, psutil" >nul 2>&1
if errorlevel 1 (
    echo 📦 安装缺失的依赖...
    postercraft_official\Scripts\pip.exe install psutil
)

REM 启动图形界面启动器
echo 🚀 启动图形界面...
postercraft_official\Scripts\python.exe postercraft_launcher.py

echo 👋 PosterCraft 启动器已退出
pause
