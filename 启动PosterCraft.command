#!/bin/bash

# PosterCraft 启动程序 (macOS双击执行版本)
# 这个文件可以在macOS上双击直接运行

# 设置终端标题
echo -ne "\033]0;PosterCraft 启动器\007"

# 清屏并显示欢迎信息
clear
echo "🎨 =================================="
echo "🎨   PosterCraft AI海报生成器"
echo "🎨 =================================="
echo ""

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
echo "📁 工作目录: $SCRIPT_DIR"
cd "$SCRIPT_DIR"

# 检查Python环境
echo ""
echo "🔍 检查运行环境..."
if [ ! -f "postercraft_official/bin/python" ]; then
    echo "❌ 错误: 未找到虚拟环境"
    echo "   请确保已正确安装PosterCraft环境"
    echo "   虚拟环境路径: $SCRIPT_DIR/postercraft_official"
    echo ""
    echo "按任意键退出..."
    read -n 1
    exit 1
fi

# 检查启动器文件
if [ ! -f "postercraft_launcher.py" ]; then
    echo "❌ 错误: 未找到启动器文件"
    echo "   请确保 postercraft_launcher.py 文件存在"
    echo ""
    echo "按任意键退出..."
    read -n 1
    exit 1
fi

echo "✅ 环境检查通过"

# 检查并安装必要的依赖
echo ""
echo "📦 检查依赖包..."
./postercraft_official/bin/python -c "import tkinter, psutil" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "📦 安装缺失的依赖包..."
    ./postercraft_official/bin/pip install psutil
    if [ $? -eq 0 ]; then
        echo "✅ 依赖包安装完成"
    else
        echo "❌ 依赖包安装失败"
        echo "按任意键退出..."
        read -n 1
        exit 1
    fi
else
    echo "✅ 依赖包检查通过"
fi

# 启动图形界面启动器
echo ""
echo "🚀 启动PosterCraft图形界面启动器..."
echo "   (启动器窗口将在几秒钟后出现)"
echo ""

# 设置环境变量
export HF_HOME="$HOME/AI_Models/PosterCraft"
export HUGGINGFACE_HUB_CACHE="$HOME/AI_Models/PosterCraft"
export TRANSFORMERS_CACHE="$HOME/AI_Models/PosterCraft"

# 启动启动器
./postercraft_official/bin/python postercraft_launcher.py

# 启动器退出后的处理
echo ""
echo "👋 PosterCraft启动器已退出"
echo "感谢使用PosterCraft AI海报生成器！"
echo ""
echo "窗口将在3秒后自动关闭..."
sleep 3
