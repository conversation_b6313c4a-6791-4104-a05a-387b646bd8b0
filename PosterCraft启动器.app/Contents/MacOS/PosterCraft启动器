#!/bin/bash

# PosterCraft 应用程序包启动器
# 这是一个macOS应用程序包，可以像普通应用一样双击启动

# 获取应用程序包的路径
APP_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/../../../" && pwd)"
echo "应用程序包路径: $APP_DIR"

# 切换到PosterCraft项目目录
cd "$APP_DIR"

# 检查是否在正确的目录
if [ ! -f "postercraft_launcher.py" ]; then
    osascript -e 'display alert "错误" message "未找到PosterCraft项目文件。请确保应用程序包位于PosterCraft项目目录中。" buttons {"确定"} default button "确定"'
    exit 1
fi

# 检查Python环境
if [ ! -f "postercraft_official/bin/python" ]; then
    osascript -e 'display alert "环境错误" message "未找到虚拟环境。请确保已正确安装PosterCraft环境。" buttons {"确定"} default button "确定"'
    exit 1
fi

# 检查并安装依赖
./postercraft_official/bin/python -c "import tkinter, psutil" 2>/dev/null
if [ $? -ne 0 ]; then
    # 显示安装依赖的对话框
    result=$(osascript -e 'display alert "安装依赖" message "需要安装缺失的依赖包，是否继续？" buttons {"取消", "安装"} default button "安装"')
    if [[ $result == *"安装"* ]]; then
        ./postercraft_official/bin/pip install psutil
        if [ $? -ne 0 ]; then
            osascript -e 'display alert "安装失败" message "依赖包安装失败，请检查网络连接或手动安装。" buttons {"确定"} default button "确定"'
            exit 1
        fi
    else
        exit 1
    fi
fi

# 设置环境变量
export HF_HOME="$HOME/AI_Models/PosterCraft"
export HUGGINGFACE_HUB_CACHE="$HOME/AI_Models/PosterCraft"
export TRANSFORMERS_CACHE="$HOME/AI_Models/PosterCraft"

# 启动图形界面启动器
./postercraft_official/bin/python postercraft_launcher.py
